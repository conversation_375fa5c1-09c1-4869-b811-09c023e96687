# 多阶段构建 Dockerfile
# 第一阶段：构建阶段
FROM maven:3.8.7-eclipse-temurin-8 AS builder

# 设置工作目录
WORKDIR /app

# 复制所有文件
COPY . .

# 构建应用
RUN mvn clean package -DskipTests -B

# 第二阶段：运行阶段
FROM eclipse-temurin:8u372-b07-jre-centos7

# 设置工作目录
WORKDIR /app

# 从构建阶段复制 JAR 文件
COPY --from=builder /app/target/user-service.jar ./user-service.jar
COPY runboot.sh .

# 设置执行权限
RUN chmod a+x runboot.sh

# 暴露端口
EXPOSE 9090

# 启动命令
CMD ["sh", "-c", "/app/runboot.sh"]
