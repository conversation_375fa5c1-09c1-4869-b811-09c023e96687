# 服务名称
spring.application.name=user-service

# 服务端口
server.port=9090

# 数据库连接URL
spring.datasource.url=**************************************************************************************

# 数据库用户名
spring.datasource.username=root

# 数据库密码
spring.datasource.password=dangerous

# 数据库驱动
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# 数据库方言
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect

# 是否显示SQL
spring.jpa.show-sql=true

# 是否自动创建表
spring.jpa.hibernate.ddl-auto=update

# Eureka Server 注册中心地址    
eureka.client.service-url.defaultZone=${EUREKA_URL:http://localhost:8080/eureka}

# 是否使用IP地址注册服务
eureka.instance.prefer-ip-address=true

# 是否向Eureka Server注册服务
eureka.client.register-with-eureka=true

# 是否从Eureka Server获取服务
eureka.client.fetch-registry=true

# 服务实例ID
eureka.instance.instance-id=${spring.application.name}:${random.value}:${server.port}

# 心跳间隔时间
eureka.instance.leaseRenewalIntervalInSeconds=5

# 心跳超时时间
eureka.instance.leaseExpirationDurationInSeconds=10

#开启优雅停机，默认是立即停机 IMMEDIATE
server.shutdown=graceful

#缓冲器即最大等待时间
spring.lifecycle.timeout-per-shutdown-phase=15s

# 禁用安全验证
management.security.enabled=false

# 指定管理端口
management.server.port=8081

# 暴露 shutdown 端点
management.endpoints.web.exposure.include=shutdown,health,info

# 指定管理端点的路径
management.endpoints.web.base-path=/actuator

# 启用 shutdown 端点
management.endpoint.shutdown.enabled=true