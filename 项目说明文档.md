# 基于云原生技术的微服务开发 - 第二次作业

## 项目概述

本项目基于 Spring Boot 和 Spring Cloud 构建了一个完整的微服务架构，包含三个核心服务：Eureka Server（服务注册中心）、User Service（用户数据服务）和 Admin Service（管理服务）。项目实现了服务注册与发现、负载均衡、数据校验等功能，并通过 Docker 容器化部署到 Kubernetes 集群。

## 项目模块说明

### 1. Eureka Server (服务注册中心)
- **端口**: 8080
- **功能**: 提供服务注册与发现功能
- **技术栈**: Spring Boot 2.3.1 + Spring Cloud Hoxton.SR6
- **配置特点**: 禁用自我保护机制，快速响应服务变化

### 2. User Service (用户数据服务)
- **端口**: 9090
- **功能**: 提供用户数据的 CRUD 操作，连接 MySQL 数据库
- **技术栈**: Spring Boot 2.3.12 + Spring Data JPA + MySQL
- **API 接口**:
  - `POST /user` - 创建用户
  - `GET /user?id={id}` - 获取用户信息
  - `GET /port` - 获取服务端口信息
- **特点**: 支持多实例部署，配置了健康检查和优雅停机

### 3. Admin Service (管理服务)
- **端口**: 10000
- **功能**: 业务管理服务，通过 OpenFeign 调用 User Service
- **技术栈**: Spring Boot 2.3.1 + OpenFeign + Ribbon
- **API 接口**:
  - `POST /user` - 通过代理创建用户（包含数据校验）
  - `GET /user?id={id}` - 通过代理获取用户信息
- **特点**: 实现了用户名和密码的非空校验，自定义负载均衡策略

## 关键技术实现

### 1. 服务注册与发现
- 所有服务启动时自动注册到 Eureka Server
- 使用 `@EnableEurekaServer` 和 `@EnableDiscoveryClient` 注解
- 配置了服务实例ID：`${spring.application.name}:${random.value}:${server.port}`

### 2. 数据校验
- 在 Admin Service 中使用 JSR-303 注解进行数据校验
- `@NotBlank` 注解确保用户名和密码不为空
- 全局异常处理器统一处理校验错误

### 3. 负载均衡
- 使用 Spring Cloud Ribbon 实现客户端负载均衡
- 自定义负载均衡规则 `CustomRule`，实现随机负载均衡策略
- User Service 部署 2 个副本，支持负载分发

### 4. 容器化部署
- 使用多阶段构建 Dockerfile 优化镜像大小
- 支持本地构建和容器内构建两种方式
- 配置了资源限制和健康检查

## 关键命令

### 1. 构建 JAR 文件
```bash
# 构建 Eureka Server
cd eureka-server
chmod +x mvnw && ./mvnw clean package -DskipTests

# 构建 User Service
cd ../user-service
chmod +x mvnw && ./mvnw clean package -DskipTests

# 构建 Admin Service
cd ../admin-service
chmod +x mvnw && ./mvnw clean package -DskipTests
```

### 2. 构建 Docker 镜像
```bash
# 构建 Eureka Server 镜像
cd eureka-server
docker build -t eureka-server:2025 .

# 构建 User Service 镜像
cd ../user-service
docker build -t user-service:2025 .

# 构建 Admin Service 镜像
cd ../admin-service
docker build -t admin-service:2025 .
```

### 3. 部署到 Kubernetes
```bash
# 部署 Eureka Server
cd eureka-server
kubectl apply -f eureka-service.yaml
kubectl apply -f eureka-deployment.yaml

# 部署 User Service
cd ../user-service
kubectl apply -f user-service.yaml
kubectl apply -f user-deployment.yaml

# 部署 Admin Service
cd ../admin-service
kubectl apply -f admin-service.yaml
kubectl apply -f admin-deployment.yaml
```

### 4. 查看部署状态
```bash
# 查看所有 Pod 状态
kubectl get pods

# 查看服务状态
kubectl get services

# 查看服务日志
kubectl logs deployment/admin-service --tail=20
```

### 5. 测试 API 功能
```bash
# 端口转发
kubectl port-forward service/admin-service 10000:10000

# 创建用户（成功案例）
curl -X POST http://localhost:10000/user \
  -H "Content-Type: application/json" \
  -d '{"name":"张三","pwd":"123456"}'

# 创建用户（用户名为空，验证失败）
curl -X POST http://localhost:10000/user \
  -H "Content-Type: application/json" \
  -d '{"name":"","pwd":"123456"}'

# 创建用户（密码为空，验证失败）
curl -X POST http://localhost:10000/user \
  -H "Content-Type: application/json" \
  -d '{"name":"李四","pwd":""}'

# 查询用户（测试负载均衡）
curl "http://localhost:10000/user?id=1"
```

## 测试结果截图说明

### 1. 成功创建用户
- **命令**: `curl -X POST http://localhost:10000/user -H "Content-Type: application/json" -d '{"name":"张三","pwd":"123456"}'`
- **结果**: `{"name":"张三","pwd":"123456"}`
- **说明**: 用户创建成功，Admin Service 通过 Feign 调用 User Service 完成数据库操作

### 2. 用户名为空验证失败
- **命令**: `curl -X POST http://localhost:10000/user -H "Content-Type: application/json" -d '{"name":"","pwd":"123456"}'`
- **结果**: `{"error":"用户名不能为空"}`
- **说明**: 数据校验正常工作，拦截了无效请求

### 3. 密码为空验证失败
- **命令**: `curl -X POST http://localhost:10000/user -H "Content-Type: application/json" -d '{"name":"李四","pwd":""}'`
- **结果**: `{"error":"密码不能为空"}`
- **说明**: 密码校验正常工作

### 4. 负载均衡测试
- **日志输出**:
  ```
  [2025-06-28 23:56:48] http-nio-10000-exec-1 - 所有可达服务器：[***********:9090, ***********:9090]
  [2025-06-28 23:56:48] http-nio-10000-exec-1 - Servers[0]: ***********:9090
  [2025-06-28 23:57:17] http-nio-10000-exec-5 - Servers[1]: ***********:9090
  ```
- **说明**: 自定义负载均衡策略正常工作，请求被分发到不同的 User Service 实例

## 服务架构图

```
Admin Service (10000) --[Feign]--> User Service (9090) --[JPA]--> MySQL (3306)
       ↓                                ↓
   Eureka Client                  Eureka Client
       ↓                                ↓
       └────────── Eureka Server (8080) ──────────┘
```

## 项目亮点

1. **完整的微服务架构**: 实现了服务注册发现、负载均衡、数据校验等核心功能
2. **多阶段构建优化**: 使用 Docker 多阶段构建减少镜像大小
3. **自定义负载均衡**: 实现了随机负载均衡策略，支持多实例部署
4. **完善的数据校验**: 使用 JSR-303 注解和全局异常处理
5. **云原生部署**: 完整的 Kubernetes 部署配置，包含健康检查和资源限制
6. **RESTful API 设计**: 遵循 REST 风格的 API 设计规范

## 总结

本项目成功实现了基于 Spring Cloud 的微服务架构，包含了服务注册发现、负载均衡、数据校验等核心功能。通过 Docker 容器化和 Kubernetes 部署，展示了完整的云原生应用开发流程。所有功能测试通过，满足作业要求。
