<!DOCTYPE html><html><head>
      <title>项目说明文档</title>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      
      <link rel="stylesheet" href="file:////home/<USER>/.vscode/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/katex/katex.min.css">
      
      
      
      
      
      <style>
      code[class*=language-],pre[class*=language-]{color:#333;background:0 0;font-family:<PERSON><PERSON><PERSON>,"Liberation Mono",Menlo,Courier,monospace;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.4;-moz-tab-size:8;-o-tab-size:8;tab-size:8;-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none}pre[class*=language-]{padding:.8em;overflow:auto;border-radius:3px;background:#f5f5f5}:not(pre)>code[class*=language-]{padding:.1em;border-radius:.3em;white-space:normal;background:#f5f5f5}.token.blockquote,.token.comment{color:#969896}.token.cdata{color:#183691}.token.doctype,.token.macro.property,.token.punctuation,.token.variable{color:#333}.token.builtin,.token.important,.token.keyword,.token.operator,.token.rule{color:#a71d5d}.token.attr-value,.token.regex,.token.string,.token.url{color:#183691}.token.atrule,.token.boolean,.token.code,.token.command,.token.constant,.token.entity,.token.number,.token.property,.token.symbol{color:#0086b3}.token.prolog,.token.selector,.token.tag{color:#63a35c}.token.attr-name,.token.class,.token.class-name,.token.function,.token.id,.token.namespace,.token.pseudo-class,.token.pseudo-element,.token.url-reference .token.variable{color:#795da3}.token.entity{cursor:help}.token.title,.token.title .token.punctuation{font-weight:700;color:#1d3e81}.token.list{color:#ed6a43}.token.inserted{background-color:#eaffea;color:#55a532}.token.deleted{background-color:#ffecec;color:#bd2c00}.token.bold{font-weight:700}.token.italic{font-style:italic}.language-json .token.property{color:#183691}.language-markup .token.tag .token.punctuation{color:#333}.language-css .token.function,code.language-css{color:#0086b3}.language-yaml .token.atrule{color:#63a35c}code.language-yaml{color:#183691}.language-ruby .token.function{color:#333}.language-markdown .token.url{color:#795da3}.language-makefile .token.symbol{color:#795da3}.language-makefile .token.variable{color:#183691}.language-makefile .token.builtin{color:#0086b3}.language-bash .token.keyword{color:#0086b3}pre[data-line]{position:relative;padding:1em 0 1em 3em}pre[data-line] .line-highlight-wrapper{position:absolute;top:0;left:0;background-color:transparent;display:block;width:100%}pre[data-line] .line-highlight{position:absolute;left:0;right:0;padding:inherit 0;margin-top:1em;background:hsla(24,20%,50%,.08);background:linear-gradient(to right,hsla(24,20%,50%,.1) 70%,hsla(24,20%,50%,0));pointer-events:none;line-height:inherit;white-space:pre}pre[data-line] .line-highlight:before,pre[data-line] .line-highlight[data-end]:after{content:attr(data-start);position:absolute;top:.4em;left:.6em;min-width:1em;padding:0 .5em;background-color:hsla(24,20%,50%,.4);color:#f4f1ef;font:bold 65%/1.5 sans-serif;text-align:center;vertical-align:.3em;border-radius:999px;text-shadow:none;box-shadow:0 1px #fff}pre[data-line] .line-highlight[data-end]:after{content:attr(data-end);top:auto;bottom:.4em}html body{font-family:'Helvetica Neue',Helvetica,'Segoe UI',Arial,freesans,sans-serif;font-size:16px;line-height:1.6;color:#333;background-color:#fff;overflow:initial;box-sizing:border-box;word-wrap:break-word}html body>:first-child{margin-top:0}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{line-height:1.2;margin-top:1em;margin-bottom:16px;color:#000}html body h1{font-size:2.25em;font-weight:300;padding-bottom:.3em}html body h2{font-size:1.75em;font-weight:400;padding-bottom:.3em}html body h3{font-size:1.5em;font-weight:500}html body h4{font-size:1.25em;font-weight:600}html body h5{font-size:1.1em;font-weight:600}html body h6{font-size:1em;font-weight:600}html body h1,html body h2,html body h3,html body h4,html body h5{font-weight:600}html body h5{font-size:1em}html body h6{color:#5c5c5c}html body strong{color:#000}html body del{color:#5c5c5c}html body a:not([href]){color:inherit;text-decoration:none}html body a{color:#08c;text-decoration:none}html body a:hover{color:#00a3f5;text-decoration:none}html body img{max-width:100%}html body>p{margin-top:0;margin-bottom:16px;word-wrap:break-word}html body>ol,html body>ul{margin-bottom:16px}html body ol,html body ul{padding-left:2em}html body ol.no-list,html body ul.no-list{padding:0;list-style-type:none}html body ol ol,html body ol ul,html body ul ol,html body ul ul{margin-top:0;margin-bottom:0}html body li{margin-bottom:0}html body li.task-list-item{list-style:none}html body li>p{margin-top:0;margin-bottom:0}html body .task-list-item-checkbox{margin:0 .2em .25em -1.8em;vertical-align:middle}html body .task-list-item-checkbox:hover{cursor:pointer}html body blockquote{margin:16px 0;font-size:inherit;padding:0 15px;color:#5c5c5c;background-color:#f0f0f0;border-left:4px solid #d6d6d6}html body blockquote>:first-child{margin-top:0}html body blockquote>:last-child{margin-bottom:0}html body hr{height:4px;margin:32px 0;background-color:#d6d6d6;border:0 none}html body table{margin:10px 0 15px 0;border-collapse:collapse;border-spacing:0;display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all}html body table th{font-weight:700;color:#000}html body table td,html body table th{border:1px solid #d6d6d6;padding:6px 13px}html body dl{padding:0}html body dl dt{padding:0;margin-top:16px;font-size:1em;font-style:italic;font-weight:700}html body dl dd{padding:0 16px;margin-bottom:16px}html body code{font-family:Menlo,Monaco,Consolas,'Courier New',monospace;font-size:.85em;color:#000;background-color:#f0f0f0;border-radius:3px;padding:.2em 0}html body code::after,html body code::before{letter-spacing:-.2em;content:'\00a0'}html body pre>code{padding:0;margin:0;word-break:normal;white-space:pre;background:0 0;border:0}html body .highlight{margin-bottom:16px}html body .highlight pre,html body pre{padding:1em;overflow:auto;line-height:1.45;border:#d6d6d6;border-radius:3px}html body .highlight pre{margin-bottom:0;word-break:normal}html body pre code,html body pre tt{display:inline;max-width:initial;padding:0;margin:0;overflow:initial;line-height:inherit;word-wrap:normal;background-color:transparent;border:0}html body pre code:after,html body pre code:before,html body pre tt:after,html body pre tt:before{content:normal}html body blockquote,html body dl,html body ol,html body p,html body pre,html body ul{margin-top:0;margin-bottom:16px}html body kbd{color:#000;border:1px solid #d6d6d6;border-bottom:2px solid #c7c7c7;padding:2px 4px;background-color:#f0f0f0;border-radius:3px}@media print{html body{background-color:#fff}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{color:#000;page-break-after:avoid}html body blockquote{color:#5c5c5c}html body pre{page-break-inside:avoid}html body table{display:table}html body img{display:block;max-width:100%;max-height:100%}html body code,html body pre{word-wrap:break-word;white-space:pre}}.markdown-preview{width:100%;height:100%;box-sizing:border-box}.markdown-preview ul{list-style:disc}.markdown-preview ul ul{list-style:circle}.markdown-preview ul ul ul{list-style:square}.markdown-preview ol{list-style:decimal}.markdown-preview ol ol,.markdown-preview ul ol{list-style-type:lower-roman}.markdown-preview ol ol ol,.markdown-preview ol ul ol,.markdown-preview ul ol ol,.markdown-preview ul ul ol{list-style-type:lower-alpha}.markdown-preview .newpage,.markdown-preview .pagebreak{page-break-before:always}.markdown-preview pre.line-numbers{position:relative;padding-left:3.8em;counter-reset:linenumber}.markdown-preview pre.line-numbers>code{position:relative}.markdown-preview pre.line-numbers .line-numbers-rows{position:absolute;pointer-events:none;top:1em;font-size:100%;left:0;width:3em;letter-spacing:-1px;border-right:1px solid #999;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.markdown-preview pre.line-numbers .line-numbers-rows>span{pointer-events:none;display:block;counter-increment:linenumber}.markdown-preview pre.line-numbers .line-numbers-rows>span:before{content:counter(linenumber);color:#999;display:block;padding-right:.8em;text-align:right}.markdown-preview .mathjax-exps .MathJax_Display{text-align:center!important}.markdown-preview:not([data-for=preview]) .code-chunk .code-chunk-btn-group{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .status{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .output-div{margin-bottom:16px}.markdown-preview .md-toc{padding:0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link div,.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}.markdown-preview .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}.scrollbar-style::-webkit-scrollbar{width:8px}.scrollbar-style::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}.scrollbar-style::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode]){position:relative;width:100%;height:100%;top:0;left:0;margin:0;padding:0;overflow:auto}html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{position:relative;top:0;min-height:100vh}@media screen and (min-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em calc(50% - 457px + 2em)}}@media screen and (max-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{font-size:14px!important;padding:1em}}@media print{html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{display:none}}html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{position:fixed;bottom:8px;left:8px;font-size:28px;cursor:pointer;color:inherit;z-index:99;width:32px;text-align:center;opacity:.4}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] #sidebar-toc-btn{opacity:1}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc{position:fixed;top:0;left:0;width:300px;height:100%;padding:32px 0 48px 0;font-size:14px;box-shadow:0 0 4px rgba(150,150,150,.33);box-sizing:border-box;overflow:auto;background-color:inherit}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar{width:8px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc a{text-decoration:none}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc{padding:0 16px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link div,html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{left:300px;width:calc(100% - 300px);padding:2em calc(50% - 457px - 300px / 2);margin:0;box-sizing:border-box}@media screen and (max-width:1274px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{width:100%}}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .markdown-preview{left:50%;transform:translateX(-50%)}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .md-sidebar-toc{display:none}
/* Please visit the URL below for more information: */
/*   https://shd101wyy.github.io/markdown-preview-enhanced/#/customize-css */

      </style>
      <!-- The content below will be included at the end of the <head> element. --><script type="text/javascript">
  document.addEventListener("DOMContentLoaded", function () {
    // your code here
  });
</script></head><body for="html-export">
    
    
      <div class="crossnote markdown-preview  ">
      
<h1 id="基于云原生技术的微服务开发---第二次作业">基于云原生技术的微服务开发 - 第二次作业 </h1>
<h2 id="项目概述">项目概述 </h2>
<p>本项目基于 Spring Boot 和 Spring Cloud 构建了一个完整的微服务架构，包含三个核心服务：Eureka Server（服务注册中心）、User Service（用户数据服务）和 Admin Service（管理服务）。项目实现了服务注册与发现、负载均衡、数据校验等功能，并通过 Docker 容器化部署到 Kubernetes 集群。</p>
<h2 id="项目模块说明">项目模块说明 </h2>
<h3 id="1-eureka-server-服务注册中心">1. Eureka Server (服务注册中心) </h3>
<ul>
<li><strong>端口</strong>: 8080</li>
<li><strong>功能</strong>: 提供服务注册与发现功能</li>
<li><strong>技术栈</strong>: Spring Boot 2.3.1 + Spring Cloud Hoxton.SR6</li>
<li><strong>配置特点</strong>: 禁用自我保护机制，快速响应服务变化</li>
</ul>
<h3 id="2-user-service-用户数据服务">2. User Service (用户数据服务) </h3>
<ul>
<li><strong>端口</strong>: 9090</li>
<li><strong>功能</strong>: 提供用户数据的 CRUD 操作，连接 MySQL 数据库</li>
<li><strong>技术栈</strong>: Spring Boot 2.3.12 + Spring Data JPA + MySQL</li>
<li><strong>API 接口</strong>:
<ul>
<li><code>POST /user</code> - 创建用户</li>
<li><code>GET /user?id={id}</code> - 获取用户信息</li>
<li><code>GET /port</code> - 获取服务端口信息</li>
</ul>
</li>
<li><strong>特点</strong>: 支持多实例部署，配置了健康检查和优雅停机</li>
</ul>
<h3 id="3-admin-service-管理服务">3. Admin Service (管理服务) </h3>
<ul>
<li><strong>端口</strong>: 10000</li>
<li><strong>功能</strong>: 业务管理服务，通过 OpenFeign 调用 User Service</li>
<li><strong>技术栈</strong>: Spring Boot 2.3.1 + OpenFeign + Ribbon</li>
<li><strong>API 接口</strong>:
<ul>
<li><code>POST /user</code> - 通过代理创建用户（包含数据校验）</li>
<li><code>GET /user?id={id}</code> - 通过代理获取用户信息</li>
</ul>
</li>
<li><strong>特点</strong>: 实现了用户名和密码的非空校验，自定义负载均衡策略</li>
</ul>
<h2 id="关键技术实现">关键技术实现 </h2>
<h3 id="1-服务注册与发现">1. 服务注册与发现 </h3>
<ul>
<li>所有服务启动时自动注册到 Eureka Server</li>
<li>使用 <code>@EnableEurekaServer</code> 和 <code>@EnableDiscoveryClient</code> 注解</li>
<li>配置了服务实例ID：<code>${spring.application.name}:${random.value}:${server.port}</code></li>
</ul>
<h3 id="2-数据校验">2. 数据校验 </h3>
<ul>
<li>在 Admin Service 中使用 JSR-303 注解进行数据校验</li>
<li><code>@NotBlank</code> 注解确保用户名和密码不为空</li>
<li>全局异常处理器统一处理校验错误</li>
</ul>
<h3 id="3-负载均衡">3. 负载均衡 </h3>
<ul>
<li>使用 Spring Cloud Ribbon 实现客户端负载均衡</li>
<li>自定义负载均衡规则 <code>CustomRule</code>，实现随机负载均衡策略</li>
<li>User Service 部署 2 个副本，支持负载分发</li>
</ul>
<h3 id="4-容器化部署">4. 容器化部署 </h3>
<ul>
<li>使用多阶段构建 Dockerfile 优化镜像大小</li>
<li>支持本地构建和容器内构建两种方式</li>
<li>配置了资源限制和健康检查</li>
</ul>
<h2 id="关键命令">关键命令 </h2>
<h3 id="1-构建-jar-文件">1. 构建 JAR 文件 </h3>
<pre data-role="codeBlock" data-info="bash" class="language-bash bash"><code><span class="token comment"># 构建 Eureka Server</span>
<span class="token builtin class-name">cd</span> eureka-server
<span class="token function">chmod</span> +x mvnw <span class="token operator">&amp;&amp;</span> ./mvnw clean package <span class="token parameter variable">-DskipTests</span>

<span class="token comment"># 构建 User Service</span>
<span class="token builtin class-name">cd</span> <span class="token punctuation">..</span>/user-service
<span class="token function">chmod</span> +x mvnw <span class="token operator">&amp;&amp;</span> ./mvnw clean package <span class="token parameter variable">-DskipTests</span>

<span class="token comment"># 构建 Admin Service</span>
<span class="token builtin class-name">cd</span> <span class="token punctuation">..</span>/admin-service
<span class="token function">chmod</span> +x mvnw <span class="token operator">&amp;&amp;</span> ./mvnw clean package <span class="token parameter variable">-DskipTests</span>
</code></pre><h3 id="2-构建-docker-镜像">2. 构建 Docker 镜像 </h3>
<pre data-role="codeBlock" data-info="bash" class="language-bash bash"><code><span class="token comment"># 构建 Eureka Server 镜像</span>
<span class="token builtin class-name">cd</span> eureka-server
<span class="token function">docker</span> build <span class="token parameter variable">-t</span> eureka-server:2025 <span class="token builtin class-name">.</span>

<span class="token comment"># 构建 User Service 镜像</span>
<span class="token builtin class-name">cd</span> <span class="token punctuation">..</span>/user-service
<span class="token function">docker</span> build <span class="token parameter variable">-t</span> user-service:2025 <span class="token builtin class-name">.</span>

<span class="token comment"># 构建 Admin Service 镜像</span>
<span class="token builtin class-name">cd</span> <span class="token punctuation">..</span>/admin-service
<span class="token function">docker</span> build <span class="token parameter variable">-t</span> admin-service:2025 <span class="token builtin class-name">.</span>
</code></pre><h3 id="3-部署到-kubernetes">3. 部署到 Kubernetes </h3>
<pre data-role="codeBlock" data-info="bash" class="language-bash bash"><code><span class="token comment"># 部署 Eureka Server</span>
<span class="token builtin class-name">cd</span> eureka-server
kubectl apply <span class="token parameter variable">-f</span> eureka-service.yaml
kubectl apply <span class="token parameter variable">-f</span> eureka-deployment.yaml

<span class="token comment"># 部署 User Service</span>
<span class="token builtin class-name">cd</span> <span class="token punctuation">..</span>/user-service
kubectl apply <span class="token parameter variable">-f</span> user-service.yaml
kubectl apply <span class="token parameter variable">-f</span> user-deployment.yaml

<span class="token comment"># 部署 Admin Service</span>
<span class="token builtin class-name">cd</span> <span class="token punctuation">..</span>/admin-service
kubectl apply <span class="token parameter variable">-f</span> admin-service.yaml
kubectl apply <span class="token parameter variable">-f</span> admin-deployment.yaml
</code></pre><h3 id="4-查看部署状态">4. 查看部署状态 </h3>
<pre data-role="codeBlock" data-info="bash" class="language-bash bash"><code><span class="token comment"># 查看所有 Pod 状态</span>
kubectl get pods

<span class="token comment"># 查看服务状态</span>
kubectl get services

<span class="token comment"># 查看服务日志</span>
kubectl logs deployment/admin-service <span class="token parameter variable">--tail</span><span class="token operator">=</span><span class="token number">20</span>
</code></pre><h3 id="5-测试-api-功能">5. 测试 API 功能 </h3>
<pre data-role="codeBlock" data-info="bash" class="language-bash bash"><code><span class="token comment"># 端口转发</span>
kubectl port-forward service/admin-service <span class="token number">10000</span>:10000

<span class="token comment"># 创建用户（成功案例）</span>
<span class="token function">curl</span> <span class="token parameter variable">-X</span> POST http://localhost:10000/user <span class="token punctuation">\</span>
  <span class="token parameter variable">-H</span> <span class="token string">"Content-Type: application/json"</span> <span class="token punctuation">\</span>
  <span class="token parameter variable">-d</span> <span class="token string">'{"name":"张三","pwd":"123456"}'</span>

<span class="token comment"># 创建用户（用户名为空，验证失败）</span>
<span class="token function">curl</span> <span class="token parameter variable">-X</span> POST http://localhost:10000/user <span class="token punctuation">\</span>
  <span class="token parameter variable">-H</span> <span class="token string">"Content-Type: application/json"</span> <span class="token punctuation">\</span>
  <span class="token parameter variable">-d</span> <span class="token string">'{"name":"","pwd":"123456"}'</span>

<span class="token comment"># 创建用户（密码为空，验证失败）</span>
<span class="token function">curl</span> <span class="token parameter variable">-X</span> POST http://localhost:10000/user <span class="token punctuation">\</span>
  <span class="token parameter variable">-H</span> <span class="token string">"Content-Type: application/json"</span> <span class="token punctuation">\</span>
  <span class="token parameter variable">-d</span> <span class="token string">'{"name":"李四","pwd":""}'</span>

<span class="token comment"># 查询用户（测试负载均衡）</span>
<span class="token function">curl</span> <span class="token string">"http://localhost:10000/user?id=1"</span>
</code></pre><h2 id="测试结果截图说明">测试结果截图说明 </h2>
<h3 id="1-成功创建用户">1. 成功创建用户 </h3>
<ul>
<li><strong>命令</strong>: <code>curl -X POST http://localhost:10000/user -H "Content-Type: application/json" -d '{"name":"张三","pwd":"123456"}'</code></li>
<li><strong>结果</strong>: <code>{"name":"张三","pwd":"123456"}</code></li>
<li><strong>说明</strong>: 用户创建成功，Admin Service 通过 Feign 调用 User Service 完成数据库操作</li>
</ul>
<h3 id="2-用户名为空验证失败">2. 用户名为空验证失败 </h3>
<ul>
<li><strong>命令</strong>: <code>curl -X POST http://localhost:10000/user -H "Content-Type: application/json" -d '{"name":"","pwd":"123456"}'</code></li>
<li><strong>结果</strong>: <code>{"error":"用户名不能为空"}</code></li>
<li><strong>说明</strong>: 数据校验正常工作，拦截了无效请求</li>
</ul>
<h3 id="3-密码为空验证失败">3. 密码为空验证失败 </h3>
<ul>
<li><strong>命令</strong>: <code>curl -X POST http://localhost:10000/user -H "Content-Type: application/json" -d '{"name":"李四","pwd":""}'</code></li>
<li><strong>结果</strong>: <code>{"error":"密码不能为空"}</code></li>
<li><strong>说明</strong>: 密码校验正常工作</li>
</ul>
<h3 id="4-负载均衡测试">4. 负载均衡测试 </h3>
<ul>
<li><strong>日志输出</strong>:<pre data-role="codeBlock" data-info="" class="language-text"><code>[2025-06-28 23:56:48] http-nio-10000-exec-1 - 所有可达服务器：[***********:9090, ***********:9090]
[2025-06-28 23:56:48] http-nio-10000-exec-1 - Servers[0]: ***********:9090
[2025-06-28 23:57:17] http-nio-10000-exec-5 - Servers[1]: ***********:9090
</code></pre></li>
<li><strong>说明</strong>: 自定义负载均衡策略正常工作，请求被分发到不同的 User Service 实例</li>
</ul>
<h2 id="服务架构图">服务架构图 </h2>
<pre data-role="codeBlock" data-info="" class="language-text"><code>Admin Service (10000) --[Feign]--&gt; User Service (9090) --[JPA]--&gt; MySQL (3306)
       ↓                                ↓
   Eureka Client                  Eureka Client
       ↓                                ↓
       └────────── Eureka Server (8080) ──────────┘
</code></pre><h2 id="项目亮点">项目亮点 </h2>
<ol>
<li><strong>完整的微服务架构</strong>: 实现了服务注册发现、负载均衡、数据校验等核心功能</li>
<li><strong>多阶段构建优化</strong>: 使用 Docker 多阶段构建减少镜像大小</li>
<li><strong>自定义负载均衡</strong>: 实现了随机负载均衡策略，支持多实例部署</li>
<li><strong>完善的数据校验</strong>: 使用 JSR-303 注解和全局异常处理</li>
<li><strong>云原生部署</strong>: 完整的 Kubernetes 部署配置，包含健康检查和资源限制</li>
<li><strong>RESTful API 设计</strong>: 遵循 REST 风格的 API 设计规范</li>
</ol>
<h2 id="总结">总结 </h2>
<p>本项目成功实现了基于 Spring Cloud 的微服务架构，包含了服务注册发现、负载均衡、数据校验等核心功能。通过 Docker 容器化和 Kubernetes 部署，展示了完整的云原生应用开发流程。所有功能测试通过，满足作业要求。</p>

      </div>
      
      
    
    
    
    
    
    
  
    </body></html>