# 服务端口
server.port=10000

# 服务名称，用于向Eureka Server注册服务 
spring.application.name=admin-servie

# Eureka Server 注册中心地址
eureka.client.service-url.defaultZone=${EUREKA_URL:http://localhost:8080/eureka}

# 是否使用IP地址注册服务
eureka.instance.prefer-ip-address=true

# 是否向Eureka Server注册服务
eureka.client.register-with-eureka=true

# 设置从 eureka 获取服务的间隔时间
eureka.client.registry-fetch-interval-seconds=5

# 是否从Eureka Server获取服务
eureka.client.fetch-registry=true

# 是否从Eureka Server获取服务
ribbon.fetch-registry=true

# 是否启用Ribbon
ribbon.enabled=true

# 设置 ribbon 从 eureka 获取服务的间隔时间
ribbon.eureka.ribbon.ServerListRefreshInterval=3000

# 自定义负载均衡规则
user-service.ribbon.NFLoadBalancerRuleClassName=io.daocloud.adminservice.config.CustomRule

# 连接超时时间
feign.client.config.default.connectTimeout=3000

# 读取超时时间
feign.client.config.default.readTimeout=30000

# 每次重试的时间间隔（毫秒）
feign.client.config.default.retryer.period=100

# 最大重试间隔（毫秒）
feign.client.config.default.retryer.maxPeriod=1000

# 最大重试次数
feign.client.config.default.retryer.maxAttempts=10

# 是否启用重试
feign.client.config.default.retryer.enabled=true