apiVersion: apps/v1
kind: Deployment
metadata:
  name: admin-service
  labels:
    app: admin-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: admin-service
  template:
    metadata:
      labels:
        app: admin-service
    spec:
      hostname: admin-service
      containers:
        - name: admin-service
          image: admin-service:2025
          imagePullPolicy: Never
          env:
            - name: EUREKA_URL
              value: http://eureka:8080/eureka
          ports:
            - containerPort: 10000
          resources:
            requests:
              cpu: 100m
              memory: 512Mi
            limits:
              cpu: 500m
              memory: 1024Mi
