apiVersion: apps/v1
kind: Deployment
metadata:
  name: eureka
  labels:
    app: eureka
spec:
  replicas: 1
  selector:
    matchLabels:
      app: eureka
  template:
    metadata:
      labels:
        app: eureka
    spec:
      hostname: eureka
      containers:
        - name: eureka
          image: eureka-server:2025
          imagePullPolicy: Never
          ports:
            - containerPort: 8080
          resources:
            requests:
              cpu: 100m
              memory: 512Mi
            limits:
              cpu: 500m
              memory: 1024Mi
