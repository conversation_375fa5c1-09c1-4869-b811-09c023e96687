# Eureka Server 监听端口
server.port=8080

# Eureka Server 注册中心地址
eureka.client.service-url.defaultZone=http://localhost:8080

# 是否从注册中心获取服务，因为Eureka Server是注册中心，所以不需要从注册中心获取服务
eureka.client.fetch-registry=false

# 是否向注册中心注册服务，因为Eureka Server是注册中心，所以不需要向自己注册
eureka.client.register-with-eureka=false

# 禁用自我保护机制
eureka.server.enable-self-preservation=false

# 服务端缓存服务列表的刷新间隔（默认 30 秒）
eureka.server.response-cache-update-interval-ms=3000